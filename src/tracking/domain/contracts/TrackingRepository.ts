import type { Either, UnexpectedError } from '@discocil/fv-domain-library/domain';
import type { EventTracking } from '../entities/EventTracking';
import type { UnsupportedEventError } from '../errors/UnsupportedEventError';

export type TrackingEither = Either<UnexpectedError | UnsupportedEventError, true>;

export type Credentials = {
  readonly accessToken: string;
  readonly pixelId: string;
};

export interface TrackingRepository {
  track: (event: EventTracking, credentials: Credentials) => Promise<TrackingEither>;
}
