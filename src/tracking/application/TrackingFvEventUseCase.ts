import {
  left,
  NotFoundError,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';

import { TrackingMongoRepository } from '../domain/contracts/TrackingMongoRepository';
import { EventMetaFactory } from '../domain/services/EventTrackingFactory';

import type { TrackingEventDto, TrackingEventEither } from '@/tracking/domain/contracts/TrackingEventUseCaseContracts';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class TrackingFvEventUseCase implements UseCase<TrackingEventDto, Promise<TrackingEventEither>> {
  constructor(
    private readonly trackingMongoRepository: TrackingMongoRepository,
    private readonly eventRepository: EventRepository,
  ) {}

  @contextualizeError()
  async execute(dto: TrackingEventDto): Promise<TrackingEventEither> {
    const eventId = dto.eventId;

    if (eventId.isEmpty()) {
      return left(NotFoundError.build({
        context: this.constructor.name,
        target: EventEntity.name,
      }).notAutoContextualizable());
    }

    const eventCriteria = EventCriteriaMother.idToMatch(
      UniqueEntityID.build(eventId.get()),
    );

    const eventOrError = await this.eventRepository.find(eventCriteria);

    if (eventOrError.isLeft()) {
      return left(eventOrError.value);
    }

    const eventFactoryOrError = EventMetaFactory.execute(dto);

    if (eventFactoryOrError.isLeft()) {
      return left(eventFactoryOrError.value);
    }
 
    const trackingEvent = eventFactoryOrError.value;

    await this.trackingMongoRepository.save(trackingEvent);

    return right(true);
  }
}
