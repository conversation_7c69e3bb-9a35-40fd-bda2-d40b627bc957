import {
  left,
  right,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import { FacebookAdsApi } from 'facebook-nodejs-business-sdk';


import { MetaEvent } from '../mappers/MetaEvent';
import { metaEventStrategies } from '../mappers/MetaEventStrategyRegistry';

import type {
  Credentials,
  TrackingEither,
  TrackingRepository,
} from '@/tracking/domain/contracts/TrackingRepository';
import type { EventTracking } from '@/tracking/domain/entities/EventTracking';

export class TrackingMetaRepository implements TrackingRepository {
  async track(event: EventTracking, credentials: Credentials): Promise<TrackingEither> {
    const connection = FacebookAdsApi.init(credentials.accessToken);

    if (!connection) {
      return left(UnexpectedError.build({
        context: this.constructor.name,
        data: { id: event.id },
      }));
    }

    const metaEventOrError = await MetaEvent.build({
      event,
      credentials,
      strategies: metaEventStrategies,
    });

    if (metaEventOrError.isLeft()) {
      return left(metaEventOrError.value);
    }

    const metaEvent = metaEventOrError.value;

    try {
      await metaEvent.track();
    } catch (_error) {
      const parsedError = _error as Error;

      let metaExceptionData = parsedError.message;

      if ('response' in parsedError) {
        const parsedErrorResponse = parsedError.response as Record<'error_user_msg' | 'error_user_title', string>;

        metaExceptionData = `${parsedError.message} => ${parsedErrorResponse.error_user_title}: ${parsedErrorResponse.error_user_msg}`;
      }

      return left(UnexpectedError.build({
        context: this.constructor.name,
        error: parsedError,
        data: { id: event.id, metaExceptionData },
      }));
    }

    return right(true);
  }
}
