import { PerformanceMeasurementDecorator } from '@discocil/fv-domain-library/application';
import { container, instanceCachingFactory } from 'tsyringe';

import { DependencyIdentifier } from '@/cross-cutting/domain/dependencyIdentifier/DependencyIdentifier';
import { EventDependencyIdentifier } from '@/events/events/domain/dependencyIdentifier/EventDependencyIdentifier';
import { MicrositeDependencyIdentifier } from '@/microsite/domain/dependencyIdentifier/MicrositeDependencyIdentifier';
import { TrackingEventUseCase } from '@/tracking/application/TrackingEventUseCase';
import { TrackingFvEventUseCase } from '@/tracking/application/TrackingFvEventUseCase';
import { TrackingDependencyIdentifier } from '@/tracking/domain/dependencyIdentifier/TrackingDependencyIdentifier';

import { TrackingMetaRepository } from '../database/repositories/TrackingMetaRepository';
import { TrackingFvMongoRepository } from '../database/repositories/TrackingMongoRepository';

import type { InternalMessageBrokerClient } from '@/cross-cutting/domain/messageBroker/InternalMessageBrokerClientContracts';
import type { DatabaseConnection } from '@/cross-cutting/infrastructure/database/repositories/MongoConnection';
import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { MicrositeRepository } from '@/microsite/domain/contracts/MicrositeRepository';
import type { TrackingMongoRepository } from '@/tracking/domain/contracts/TrackingMongoRepository';
import type { Logger } from '@discocil/fv-domain-library/domain';
import type { DependencyContainer } from 'tsyringe';

export const TrackingContainer = {
  register: (): void => {
    container.register(TrackingDependencyIdentifier.TrackingRepository, {
      useFactory: instanceCachingFactory(() => {
        return new TrackingMetaRepository();
      }),
    });

    container.register(TrackingDependencyIdentifier.TrackingMongoRepository, {
      useFactory: instanceCachingFactory(() => {
        const dbConnection = container.resolve<DatabaseConnection>(DependencyIdentifier.DatabaseConnection);

        return new TrackingFvMongoRepository(dbConnection);
      }),
    });

    container.register(TrackingEventUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new TrackingEventUseCase(
          container.resolve<TrackingMetaRepository>(TrackingDependencyIdentifier.TrackingRepository),
          container.resolve<MicrositeRepository>(MicrositeDependencyIdentifier.MicrositeRepository),
          container.resolve<InternalMessageBrokerClient>(DependencyIdentifier.InternalMessageBrokerClient),
        );

        return new PerformanceMeasurementDecorator(
          TrackingEventUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });

    container.register(TrackingFvEventUseCase, {
      useFactory: <T>(container: DependencyContainer): T => {
        const useCase = new TrackingFvEventUseCase(
          container.resolve<TrackingMongoRepository>(TrackingDependencyIdentifier.TrackingRepository),
          container.resolve<EventRepository>(EventDependencyIdentifier.EventRepository),
        );

        return new PerformanceMeasurementDecorator(
          TrackingFvEventUseCase.name,
          container.resolve<Logger>(DependencyIdentifier.Logger),
          useCase,
        ) as T;
      },
    });
  },
};
